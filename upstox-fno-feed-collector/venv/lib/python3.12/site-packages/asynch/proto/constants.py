# DSN default values
DEFAULT_USER = "default"
DEFAULT_PASSWORD = ""  # nosec: B105

DEFAULT_HOST = "127.0.0.1"
DEFAULT_PORT = 9000
DEFAULT_SECURE_PORT = 9440

DEFAULT_DATABASE = "default"

# Pool default values
POOL_MIN_SIZE = 1
POOL_MAX_SIZE = 10

DBMS_MIN_REVISION_WITH_TEMPORARY_TABLES = 50264
DBMS_MIN_REVISION_WITH_TOTAL_ROWS_IN_PROGRESS = 51554
DBMS_MIN_REVISION_WITH_BLOCK_INFO = 51903
# Legacy above.
DBMS_MIN_REVISION_WITH_CLIENT_INFO = 54032
DBMS_MIN_REVISION_WITH_SERVER_TIMEZONE = 54058
DBMS_MIN_REVISION_WITH_QUOTA_KEY_IN_CLIENT_INFO = 54060
DBMS_MIN_REVISION_WITH_SERVER_DISPLAY_NAME = 54372
DBMS_MIN_REVISION_WITH_VERSION_PATCH = 54401
DBMS_MIN_REVISION_WITH_SERVER_LOGS = 54406
DBMS_MIN_REVISION_WITH_COLUMN_DEFAULTS_METADATA = 54410
DBMS_MIN_REVISION_WITH_CLIENT_WRITE_INFO = 54420
DBMS_MIN_REVISION_WITH_SETTINGS_SERIALIZED_AS_STRINGS = 54429
DBMS_MIN_REVISION_WITH_INTERSERVER_SECRET = 54441
DBMS_MIN_REVISION_WITH_OPENTELEMETRY = 54442
DBMS_MIN_PROTOCOL_VERSION_WITH_DISTRIBUTED_DEPTH = 54448
DBMS_MIN_PROTOCOL_VERSION_WITH_INITIAL_QUERY_START_TIME = 54449
DBMS_MIN_PROTOCOL_VERSION_WITH_INCREMENTAL_PROFILE_EVENTS = 54451
DBMS_MIN_REVISION_WITH_PARALLEL_REPLICAS = 54453

# Timeouts
DBMS_DEFAULT_CONNECT_TIMEOUT_SEC = 10
DBMS_DEFAULT_TIMEOUT_SEC = 300

DBMS_DEFAULT_SYNC_REQUEST_TIMEOUT_SEC = 5

DEFAULT_COMPRESS_BLOCK_SIZE = 1048576
DEFAULT_INSERT_BLOCK_SIZE = 1048576

DBMS_NAME = "ClickHouse"
CLIENT_NAME = "asynch"
CLIENT_VERSION_MAJOR = 20
CLIENT_VERSION_MINOR = 10
CLIENT_VERSION_PATCH = 2
CLIENT_REVISION = 54453

BUFFER_SIZE = 1048576

STRINGS_ENCODING = "utf-8"
