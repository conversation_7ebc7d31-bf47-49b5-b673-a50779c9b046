"""
Feed Data Processor

This module processes incoming protobuf messages from WebSocket connections,
decodes them, and prepares flattened data for ClickHouse insertion.
"""

import asyncio
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Callable
import pandas as pd
from loguru import logger

from ..data.protobuf.market_data_pb2 import MarketDataDecoder
from ..database.clickhouse_client import ClickHouseClient


class FeedDataProcessor:
    """Processes market data feed messages and stores them in database."""

    def __init__(self, config: Dict[str, Any], db_client: ClickHouseClient,
                 instrument_lookup: Dict[str, Dict[str, Any]]):
        """
        Initialize feed data processor.

        Args:
            config: Configuration dictionary
            db_client: ClickHouse database client
            instrument_lookup: Dictionary mapping instrument keys to instrument details
        """
        self.config = config
        self.db_client = db_client
        self.instrument_lookup = instrument_lookup
        self.decoder = MarketDataDecoder()

        # Batch processing configuration
        self.batch_size = config.get('batch_size', 100)
        self.batch_timeout = config.get('batch_timeout', 5)  # seconds

        # Data buffers
        self.data_buffer: List[Dict[str, Any]] = []
        self.last_batch_time = datetime.now(timezone.utc)

        # Statistics
        self.messages_processed = 0
        self.messages_failed = 0
        self.last_message_time: Optional[datetime] = None

        # Start batch processing task
        self._batch_task = asyncio.create_task(self._batch_processor())

    async def process_message(self, connection_id: str, message_data: bytes):
        """
        Process incoming protobuf message.

        Args:
            connection_id: WebSocket connection ID
            message_data: Binary protobuf message data
        """
        try:
            # Decode protobuf message
            logger.info(f"Processing message from {connection_id}")
            decoded_data = self.decoder.decode_feed_response(message_data)
            logger.info(f"Decoded data: {decoded_data}")

            if not decoded_data:
                logger.debug(f"Failed to decode message from {connection_id}")
                self.messages_failed += 1
                return

            # Process feeds
            await self._process_feeds(connection_id, decoded_data)

            self.messages_processed += 1
            self.last_message_time = datetime.now(timezone.utc)

        except Exception as e:
            logger.error(f"Error processing message from {connection_id}: {e}")
            self.messages_failed += 1

    async def _process_feeds(self, connection_id: str, decoded_data: Dict[str, Any]):
        """
        Process decoded feed data.

        Args:
            connection_id: WebSocket connection ID
            decoded_data: Decoded protobuf data
        """
        try:
            feeds = decoded_data.get('feeds', {})
            current_ts = decoded_data.get('currentTs')
            feed_type = decoded_data.get('type', 'live_feed')

            if not feeds:
                return

            # Convert timestamp
            if current_ts:
                current_datetime = datetime.fromtimestamp(current_ts / 1000, tz=timezone.utc)
            else:
                current_datetime = datetime.now(timezone.utc)

            # Process each instrument feed
            for instrument_key, feed_data in feeds.items():
                flattened_data = self._flatten_feed_data(
                    instrument_key, feed_data, current_datetime, feed_type
                )

                if flattened_data:
                    self.data_buffer.append(flattened_data)

            # Check if we should flush the buffer
            if (len(self.data_buffer) >= self.batch_size or
                self._should_flush_buffer()):
                await self._flush_buffer()

        except Exception as e:
            logger.error(f"Error processing feeds from {connection_id}: {e}")

    def _flatten_feed_data(self, instrument_key: str, feed_data: Dict[str, Any],
                          current_ts: datetime, feed_type: str) -> Optional[Dict[str, Any]]:
        """
        Flatten feed data for database insertion.

        Args:
            instrument_key: Instrument key
            feed_data: Feed data from protobuf
            current_ts: Current timestamp
            feed_type: Type of feed

        Returns:
            Dict: Flattened data or None if processing failed
        """
        try:
            # Get instrument details
            instrument_info = self.instrument_lookup.get(instrument_key, {})

            # Initialize flattened data
            flattened = {
                'instrument_key': instrument_key,
                'exchange_token': instrument_info.get('exchange_token', ''),
                'trading_symbol': instrument_info.get('trading_symbol', ''),
                'underlying_symbol': instrument_info.get('underlying_symbol', ''),
                'instrument_type': instrument_info.get('instrument_type', ''),
                'strike_price': float(instrument_info.get('strike_price', 0.0)),
                'expiry': instrument_info.get('expiry'),
                'current_ts': current_ts,
                'fetch_time': datetime.now(timezone.utc),
                'feed_type': feed_type
            }

            # Process firstLevelWithGreeks data
            first_level_data = feed_data.get('firstLevelWithGreeks')
            if first_level_data:
                # LTPC data
                ltpc = first_level_data.get('ltpc', {})
                flattened.update({
                    'ltp': float(ltpc.get('ltp', 0.0)),
                    'ltt': self._convert_timestamp(ltpc.get('ltt')),
                    'ltq': int(ltpc.get('ltq', 0)),
                    'cp': float(ltpc.get('cp', 0.0))
                })

                # First depth (bid/ask)
                first_depth = first_level_data.get('firstDepth', {})
                flattened.update({
                    'bid_quantity': int(first_depth.get('bidQ', 0)),
                    'bid_price': float(first_depth.get('bidP', 0.0)),
                    'ask_quantity': int(first_depth.get('askQ', 0)),
                    'ask_price': float(first_depth.get('askP', 0.0))
                })

                # Option Greeks
                option_greeks = first_level_data.get('optionGreeks', {})
                flattened.update({
                    'delta': float(option_greeks.get('delta', 0.0)),
                    'theta': float(option_greeks.get('theta', 0.0)),
                    'gamma': float(option_greeks.get('gamma', 0.0)),
                    'vega': float(option_greeks.get('vega', 0.0)),
                    'rho': float(option_greeks.get('rho', 0.0))
                })

                # Additional fields
                flattened.update({
                    'volume_traded_today': int(first_level_data.get('vtt', 0)),
                    'open_interest': float(first_level_data.get('oi', 0.0)),
                    'implied_volatility': float(first_level_data.get('iv', 0.0))
                })

            # Process simple LTPC data (for futures or when Greeks not available)
            elif 'ltpc' in feed_data:
                ltpc = feed_data.get('ltpc', {})
                flattened.update({
                    'ltp': float(ltpc.get('ltp', 0.0)),
                    'ltt': self._convert_timestamp(ltpc.get('ltt')),
                    'ltq': int(ltpc.get('ltq', 0)),
                    'cp': float(ltpc.get('cp', 0.0)),
                    # Set default values for missing fields
                    'bid_quantity': 0,
                    'bid_price': 0.0,
                    'ask_quantity': 0,
                    'ask_price': 0.0,
                    'delta': 0.0,
                    'theta': 0.0,
                    'gamma': 0.0,
                    'vega': 0.0,
                    'rho': 0.0,
                    'volume_traded_today': 0,
                    'open_interest': 0.0,
                    'implied_volatility': 0.0
                })

            return flattened

        except Exception as e:
            logger.error(f"Error flattening feed data for {instrument_key}: {e}")
            return None

    def _convert_timestamp(self, timestamp: Optional[int]) -> Optional[datetime]:
        """
        Convert timestamp to datetime.

        Args:
            timestamp: Timestamp in milliseconds

        Returns:
            datetime: Converted datetime or None
        """
        if timestamp:
            try:
                return datetime.fromtimestamp(timestamp / 1000, tz=timezone.utc)
            except (ValueError, OSError):
                return None
        return None

    def _should_flush_buffer(self) -> bool:
        """
        Check if buffer should be flushed based on timeout.

        Returns:
            bool: True if buffer should be flushed
        """
        if not self.data_buffer:
            return False

        time_since_last_batch = (
            datetime.now(timezone.utc) - self.last_batch_time
        ).total_seconds()

        return time_since_last_batch >= self.batch_timeout

    async def _flush_buffer(self):
        """Flush data buffer to database."""
        if not self.data_buffer:
            return

        try:
            # Copy buffer and clear it
            data_to_insert = self.data_buffer.copy()
            self.data_buffer.clear()
            self.last_batch_time = datetime.now(timezone.utc)

            # Insert data into database
            success = self.db_client.insert_option_greeks_feed(data_to_insert)

            if success:
                logger.debug(f"Inserted {len(data_to_insert)} records to database")
            else:
                logger.error(f"Failed to insert {len(data_to_insert)} records")

        except Exception as e:
            logger.error(f"Error flushing buffer to database: {e}")

    async def _batch_processor(self):
        """Background task to periodically flush buffer."""
        try:
            while True:
                await asyncio.sleep(self.batch_timeout)

                if self._should_flush_buffer():
                    await self._flush_buffer()

        except asyncio.CancelledError:
            logger.info("Batch processor task cancelled")
            # Flush remaining data
            await self._flush_buffer()
        except Exception as e:
            logger.error(f"Error in batch processor: {e}")

    async def shutdown(self):
        """Shutdown the feed processor."""
        try:
            # Cancel batch processing task
            if self._batch_task and not self._batch_task.done():
                self._batch_task.cancel()
                try:
                    await self._batch_task
                except asyncio.CancelledError:
                    pass

            # Flush remaining data
            await self._flush_buffer()

            logger.info("Feed processor shutdown complete")

        except Exception as e:
            logger.error(f"Error during feed processor shutdown: {e}")

    def update_instrument_lookup(self, instrument_lookup: Dict[str, Dict[str, Any]]):
        """
        Update instrument lookup dictionary.

        Args:
            instrument_lookup: New instrument lookup dictionary
        """
        self.instrument_lookup = instrument_lookup
        logger.info(f"Updated instrument lookup with {len(instrument_lookup)} instruments")

    def get_statistics(self) -> Dict[str, Any]:
        """
        Get processing statistics.

        Returns:
            Dict: Statistics dictionary
        """
        return {
            'messages_processed': self.messages_processed,
            'messages_failed': self.messages_failed,
            'buffer_size': len(self.data_buffer),
            'last_message_time': self.last_message_time.isoformat() if self.last_message_time else None,
            'last_batch_time': self.last_batch_time.isoformat(),
            'success_rate': (
                self.messages_processed / (self.messages_processed + self.messages_failed)
                if (self.messages_processed + self.messages_failed) > 0 else 0
            )
        }

    def reset_statistics(self):
        """Reset processing statistics."""
        self.messages_processed = 0
        self.messages_failed = 0
        self.last_message_time = None
        logger.info("Feed processor statistics reset")