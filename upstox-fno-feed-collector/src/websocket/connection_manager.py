"""
WebSocket Connection Manager

This module manages multiple WebSocket connections to Upstox for market data feed.
Supports up to 5 concurrent connections with 3000 instruments each.
"""

import asyncio
import json
import uuid
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Callable, Set
import httpx
import websockets
from websockets.exceptions import ConnectionClosed, WebSocketException
from loguru import logger

from ..auth.upstox_auth import UpstoxAuthenticator


class WebSocketConnection:
    """Individual WebSocket connection handler."""

    def __init__(self, connection_id: str, auth: UpstoxAuthenticator,
                 message_handler: Callable[[str, bytes], None]):
        """
        Initialize WebSocket connection.

        Args:
            connection_id: Unique connection identifier
            auth: Upstox authenticator
            message_handler: Callback for handling received messages
        """
        self.connection_id = connection_id
        self.auth = auth
        self.message_handler = message_handler
        self.websocket: Optional[websockets.WebSocketServerProtocol] = None
        self.subscribed_instruments: Set[str] = set()
        self.is_connected = False
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        self.reconnect_delay = 5  # seconds
        self.ws_url = None

    async def get_websocket_url(self) -> Optional[str]:
        """
        Get authorized WebSocket URL from Upstox API.

        Returns:
            Optional[str]: Authorized WebSocket URL if successful, None otherwise
        """
        try:
            if not self.auth.is_token_valid():
                logger.error(f"Connection {self.connection_id}: No valid token available")
                return None

            headers = self.auth.get_websocket_headers()
            auth_url = "https://api.upstox.com/v3/feed/market-data-feed/authorize"

            # Create httpx client with provided headers
            async with httpx.AsyncClient() as client:
                response = await client.get(auth_url, headers=headers)
                response.raise_for_status()
                
                data = response.json()
                if data.get("status") == "success" and "data" in data:
                    ws_url = data["data"].get("authorized_redirect_uri")
                    if ws_url:
                        logger.success(f"Connection {self.connection_id}: Got authorized WebSocket URL")
                        return ws_url

                logger.error(f"Connection {self.connection_id}: Invalid response format")
                return None

        except Exception as e:
            logger.error(f"Connection {self.connection_id}: Failed to get WebSocket URL: {e}")
            return None

    async def connect(self) -> bool:
        """
        Establish WebSocket connection.

        Returns:
            bool: True if connection successful
        """
        try:
            # Get authorized WebSocket URL
            self.ws_url = await self.get_websocket_url()
            if not self.ws_url:
                logger.error(f"Connection {self.connection_id}: Failed to get WebSocket URL")
                return False

            headers = self.auth.get_websocket_headers()
            logger.info(f"Connection {self.connection_id}: Connecting to {self.ws_url}")

            # Create headers list for websockets library
            headers_list = []
            for key, value in headers.items():
                headers_list.append((key, value))

            self.websocket = await websockets.connect(
                self.ws_url,
                additional_headers=headers_list,
                ping_interval=20,
                ping_timeout=10,
                close_timeout=10
            )

            self.is_connected = True
            self.reconnect_attempts = 0

            logger.success(f"Connection {self.connection_id}: Connected successfully")

            # Start message listening task
            asyncio.create_task(self._listen_for_messages())

            return True

        except Exception as e:
            logger.error(f"Connection {self.connection_id}: Failed to connect: {e}")
            self.is_connected = False
            return False

    async def disconnect(self):
        """Disconnect WebSocket connection."""
        try:
            self.is_connected = False

            if self.websocket:
                await self.websocket.close()
                self.websocket = None

            logger.info(f"Connection {self.connection_id}: Disconnected")

        except Exception as e:
            logger.error(f"Connection {self.connection_id}: Error during disconnect: {e}")

    async def _listen_for_messages(self):
        """Listen for incoming WebSocket messages."""
        try:
            while self.is_connected and self.websocket:
                try:
                    message = await self.websocket.recv()
                    #logger.info(f"Connection {self.connection_id}: Received message: {message}")

                    # Handle binary protobuf messages
                    if isinstance(message, bytes):
                        await self.message_handler(self.connection_id, message)
                    else:
                        # Handle text messages (usually control messages)
                        logger.debug(f"Connection {self.connection_id}: Received text message: {message}")

                except ConnectionClosed:
                    logger.warning(f"Connection {self.connection_id}: Connection closed")
                    self.is_connected = False
                    break
                except WebSocketException as e:
                    logger.error(f"Connection {self.connection_id}: WebSocket error: {e}")
                    self.is_connected = False
                    break

        except Exception as e:
            logger.error(f"Connection {self.connection_id}: Error in message listener: {e}")
            self.is_connected = False

        # Attempt reconnection if needed
        if not self.is_connected:
            await self._attempt_reconnection()

    async def _attempt_reconnection(self):
        """Attempt to reconnect WebSocket."""
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            logger.error(f"Connection {self.connection_id}: Max reconnection attempts reached")
            return

        self.reconnect_attempts += 1
        logger.info(f"Connection {self.connection_id}: Attempting reconnection {self.reconnect_attempts}/{self.max_reconnect_attempts}")

        await asyncio.sleep(self.reconnect_delay)

        if await self.connect():
            # Re-subscribe to instruments
            if self.subscribed_instruments:
                await self.subscribe_instruments(list(self.subscribed_instruments))

    async def subscribe_instruments(self, instrument_keys: List[str]) -> bool:
        """
        Subscribe to instrument feeds.

        Args:
            instrument_keys: List of instrument keys to subscribe

        Returns:
            bool: True if subscription successful
        """
        try:
            if not self.is_connected or not self.websocket:
                logger.error(f"Connection {self.connection_id}: Not connected")
                return False

            # Limit to 3000 instruments per connection
            if len(instrument_keys) > 3000:
                logger.warning(f"Connection {self.connection_id}: Limiting to 3000 instruments")
                instrument_keys = instrument_keys[:3000]

            subscription_message = {
                "guid": str(uuid.uuid4()),
                "method": "sub",
                "data": {
                    "mode": "option_greeks",
                    "instrumentKeys": ["NSE_INDEX|Nifty Bank", "NSE_INDEX|Nifty 50"]
                    
                }
            }
            logger.info(f"Connection {self.connection_id}: Sending subscription message: {subscription_message}")
            await self.websocket.send(json.dumps(subscription_message).encode('utf-8'))

            # Update subscribed instruments
            self.subscribed_instruments.update(instrument_keys)

            logger.success(f"Connection {self.connection_id}: Subscribed to {len(instrument_keys)} instruments")
            return True

        except Exception as e:
            logger.error(f"Connection {self.connection_id}: Failed to subscribe: {e}")
            return False

    async def unsubscribe_instruments(self, instrument_keys: List[str]) -> bool:
        """
        Unsubscribe from instrument feeds.

        Args:
            instrument_keys: List of instrument keys to unsubscribe

        Returns:
            bool: True if unsubscription successful
        """
        try:
            if not self.is_connected or not self.websocket:
                logger.error(f"Connection {self.connection_id}: Not connected")
                return False

            unsubscription_message = {
                "guid": str(uuid.uuid4()),
                "method": "unsub",
                "data": {
                    "mode": "option_greeks",
                    "instrumentKeys": instrument_keys
                }
            }

            await self.websocket.send(json.dumps(unsubscription_message).encode('utf-8'))

            # Update subscribed instruments
            self.subscribed_instruments.difference_update(instrument_keys)

            logger.success(f"Connection {self.connection_id}: Unsubscribed from {len(instrument_keys)} instruments")
            return True

        except Exception as e:
            logger.error(f"Connection {self.connection_id}: Failed to unsubscribe: {e}")
            return False

    def get_subscribed_count(self) -> int:
        """Get number of subscribed instruments."""
        return len(self.subscribed_instruments)

    def get_status(self) -> Dict[str, Any]:
        """Get connection status."""
        return {
            'connection_id': self.connection_id,
            'is_connected': self.is_connected,
            'subscribed_count': len(self.subscribed_instruments),
            'reconnect_attempts': self.reconnect_attempts
        }


class WebSocketConnectionManager:
    """Manages multiple WebSocket connections for market data feed."""

    def __init__(self, config: Dict[str, Any], auth: UpstoxAuthenticator,
                 message_handler: Callable[[str, bytes], None]):
        """
        Initialize WebSocket connection manager.

        Args:
            config: WebSocket configuration
            auth: Upstox authenticator
            message_handler: Callback for handling received messages
        """
        self.config = config
        self.auth = auth
        self.message_handler = message_handler
        self.max_connections = config.get('max_connections', 5)
        self.max_instruments_per_connection = config.get('max_instruments_per_connection', 3000)

        # Connection pool
        self.connections: Dict[str, WebSocketConnection] = {}
        self.connection_counter = 0

        # Instrument distribution
        self.instrument_distribution: Dict[str, List[str]] = {}

    async def initialize_connections(self) -> bool:
        """
        Initialize all WebSocket connections.

        Returns:
            bool: True if all connections initialized successfully
        """
        try:
            logger.info(f"Initializing {self.max_connections} WebSocket connections")

            success_count = 0
            for i in range(self.max_connections):
                #sleep for 1 second
                await asyncio.sleep(1)
                connection_id = f"ws_conn_{i+1}"

                connection = WebSocketConnection(
                    connection_id=connection_id,
                    auth=self.auth,
                    message_handler=self.message_handler
                )

                if await connection.connect():
                    self.connections[connection_id] = connection
                    success_count += 1
                    logger.success(f"Connection {connection_id} initialized")
                else:
                    logger.error(f"Failed to initialize connection {connection_id}")

            logger.info(f"Initialized {success_count}/{self.max_connections} connections")
            return success_count > 0

        except Exception as e:
            logger.error(f"Error initializing connections: {e}")
            return False

    async def distribute_and_subscribe(self, instrument_keys: List[str]) -> bool:
        """
        Distribute instruments across connections and subscribe.

        Args:
            instrument_keys: List of instrument keys to subscribe

        Returns:
            bool: True if subscription successful
        """
        try:
            if not self.connections:
                logger.error("No active connections available")
                return False

            # Clear previous distributions
            self.instrument_distribution.clear()

            # Distribute instruments across connections
            connection_ids = list(self.connections.keys())
            instruments_per_connection = self.max_instruments_per_connection

            for i, connection_id in enumerate(connection_ids):
                start_idx = i * instruments_per_connection
                end_idx = min(start_idx + instruments_per_connection, len(instrument_keys))

                if start_idx >= len(instrument_keys):
                    break

                connection_instruments = instrument_keys[start_idx:end_idx]
                self.instrument_distribution[connection_id] = connection_instruments

                logger.info(f"Assigning {len(connection_instruments)} instruments to {connection_id}")

            # Subscribe to instruments on each connection
            subscription_tasks = []
            for connection_id, instruments in self.instrument_distribution.items():
                if connection_id in self.connections:
                    task = self.connections[connection_id].subscribe_instruments(instruments)
                    subscription_tasks.append(task)

            # Wait for all subscriptions to complete
            results = await asyncio.gather(*subscription_tasks, return_exceptions=True)

            success_count = sum(1 for result in results if result is True)
            total_subscribed = sum(len(instruments) for instruments in self.instrument_distribution.values())

            logger.success(f"Subscribed to {total_subscribed} instruments across {success_count} connections")
            return success_count > 0

        except Exception as e:
            logger.error(f"Error distributing and subscribing instruments: {e}")
            return False

    async def unsubscribe_all(self) -> bool:
        """
        Unsubscribe from all instruments on all connections.

        Returns:
            bool: True if unsubscription successful
        """
        try:
            unsubscription_tasks = []

            for connection_id, instruments in self.instrument_distribution.items():
                if connection_id in self.connections and instruments:
                    task = self.connections[connection_id].unsubscribe_instruments(instruments)
                    unsubscription_tasks.append(task)

            if unsubscription_tasks:
                results = await asyncio.gather(*unsubscription_tasks, return_exceptions=True)
                success_count = sum(1 for result in results if result is True)

                logger.success(f"Unsubscribed from instruments on {success_count} connections")

            # Clear distribution
            self.instrument_distribution.clear()
            return True

        except Exception as e:
            logger.error(f"Error unsubscribing from instruments: {e}")
            return False

    async def close_all_connections(self):
        """Close all WebSocket connections."""
        try:
            logger.info("Closing all WebSocket connections")

            close_tasks = []
            for connection in self.connections.values():
                close_tasks.append(connection.disconnect())

            if close_tasks:
                await asyncio.gather(*close_tasks, return_exceptions=True)

            self.connections.clear()
            self.instrument_distribution.clear()

            logger.success("All WebSocket connections closed")

        except Exception as e:
            logger.error(f"Error closing connections: {e}")

    def get_connection_status(self) -> Dict[str, Any]:
        """
        Get status of all connections.

        Returns:
            Dict: Connection status information
        """
        status = {
            'total_connections': len(self.connections),
            'active_connections': sum(1 for conn in self.connections.values() if conn.is_connected),
            'total_subscribed_instruments': sum(conn.get_subscribed_count() for conn in self.connections.values()),
            'connections': {}
        }

        for connection_id, connection in self.connections.items():
            status['connections'][connection_id] = connection.get_status()

        return status

    def is_healthy(self) -> bool:
        """
        Check if connection manager is healthy.

        Returns:
            bool: True if at least one connection is active
        """
        return any(conn.is_connected for conn in self.connections.values())