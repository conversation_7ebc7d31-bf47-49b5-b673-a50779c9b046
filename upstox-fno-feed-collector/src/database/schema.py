"""
ClickHouse Database Schema Definitions

This module contains all the table schemas for the Upstox FnO feed collector.
"""

from typing import Dict, Any
from loguru import logger


class ClickHouseSchema:
    """ClickHouse table schema definitions and management."""

    @staticmethod
    def get_instrument_master_schema() -> str:
        """
        Get the CREATE TABLE statement for instrument master data.

        Returns:
            str: CREATE TABLE SQL statement
        """
        return """
        CREATE TABLE IF NOT EXISTS instrument_master (
            instrument_key String,
            exchange_token String,
            trading_symbol String,
            name String,
            exchange String,
            segment String,
            instrument_type String,
            underlying_symbol String,
            underlying_key String,
            underlying_type String,
            strike_price Float64,
            tick_size Float64,
            lot_size UInt32,
            minimum_lot UInt32,
            freeze_quantity Float64,
            expiry DateTime64(3, 'Asia/Kolkata'),
            weekly UInt32,
            created_at DateTime64(3, 'Asia/Kolkata') DEFAULT now(),
            updated_at DateTime64(3, 'Asia/Kolkata') DEFAULT now()
        ) ENGINE = ReplacingMergeTree(updated_at)
        ORDER BY (exchange, instrument_type, underlying_symbol, expiry, strike_price)
        PARTITION BY toYYYYMM(expiry)
        """

    @staticmethod
    def get_option_greeks_feed_schema() -> str:
        """
        Get the CREATE TABLE statement for option Greeks feed data.

        Returns:
            str: CREATE TABLE SQL statement
        """
        return """
        CREATE TABLE IF NOT EXISTS option_greeks_feed (
            instrument_key String,
            exchange_token String,
            trading_symbol String,
            underlying_symbol String,
            instrument_type String,
            lot_size UInt32,
            weekly UInt32,

            strike_price Float64,
            expiry Date,

            -- LTPC (Last Traded Price and Quantity)
            ltp Float64,
            ltt DateTime64(3, 'Asia/Kolkata'),
            ltq UInt64,
            cp Float64,

            -- First Depth (Bid/Ask)
            bid_quantity UInt64,
            bid_price Float64,
            ask_quantity UInt64,
            ask_price Float64,

            -- Option Greeks
            delta Float64,
            theta Float64,
            gamma Float64,
            vega Float64,
            rho Float64,

            -- Additional Fields
            volume_traded_today UInt64,
            open_interest UInt64,
            implied_volatility Float64,

            -- Timestamps
            current_ts DateTime64(3, 'Asia/Kolkata'),
            fetch_time DateTime64(3, 'Asia/Kolkata') DEFAULT now(),

            -- Metadata
            feed_type String DEFAULT 'live_feed'
        ) ENGINE = MergeTree()
        ORDER BY (underlying_symbol, instrument_type, expiry, strike_price, current_ts)
        PARTITION BY (toYYYYMM(expiry), underlying_symbol)
        """

    @staticmethod
    def get_equity_prices_view_schema() -> str:
        """
        Get the CREATE VIEW statement for equity prices from AngleOne data.
        This view helps in ATM strike calculation.

        Returns:
            str: CREATE VIEW SQL statement
        """
        return """
        CREATE VIEW IF NOT EXISTS equity_latest_prices AS
        SELECT
            symbol,
            argMax(lastPrice, jobDateTime) AS last_price,
            argMax(jobDateTime, jobDateTime) AS latest_job_datetime
        FROM angleone.view_equity_aone
        WHERE expiryType = 'EQ'
        GROUP BY symbol
        ORDER BY latest_job_datetime DESC
        """

    @staticmethod
    def get_subscription_log_schema() -> str:
        """
        Get the CREATE TABLE statement for subscription logging.

        Returns:
            str: CREATE TABLE SQL statement
        """
        return """
        CREATE TABLE IF NOT EXISTS subscription_log (
            subscription_id String,
            action String,  -- 'subscribe' or 'unsubscribe'
            instrument_keys Array(String),
            connection_id String,
            timestamp DateTime64(3, 'Asia/Kolkata') DEFAULT now(),
            status String,  -- 'success', 'failed', 'partial'
            error_message String DEFAULT '',
            instruments_count UInt32
        ) ENGINE = MergeTree()
        ORDER BY (timestamp, subscription_id)
        PARTITION BY toYYYYMM(timestamp)
        """

    @staticmethod
    def get_all_schemas() -> Dict[str, str]:
        """
        Get all table schemas as a dictionary.

        Returns:
            Dict[str, str]: Dictionary mapping table names to CREATE statements
        """
        return {
            'instrument_master': ClickHouseSchema.get_instrument_master_schema(),
            'option_greeks_feed': ClickHouseSchema.get_option_greeks_feed_schema(),
            'equity_latest_prices': ClickHouseSchema.get_equity_prices_view_schema(),
            'subscription_log': ClickHouseSchema.get_subscription_log_schema()
        }

    @staticmethod
    def get_indexes() -> Dict[str, str]:
        """
        Get additional indexes for better query performance.

        Returns:
            Dict[str, str]: Dictionary mapping index names to CREATE INDEX statements
        """
        return {
            'idx_option_greeks_symbol_time': """
                CREATE INDEX IF NOT EXISTS idx_option_greeks_symbol_time
                ON option_greeks_feed (underlying_symbol, current_ts)
                TYPE minmax GRANULARITY 1
            """,
            'idx_instrument_master_symbol': """
                CREATE INDEX IF NOT EXISTS idx_instrument_master_symbol
                ON instrument_master (underlying_symbol, instrument_type)
                TYPE bloom_filter GRANULARITY 1
            """
        }