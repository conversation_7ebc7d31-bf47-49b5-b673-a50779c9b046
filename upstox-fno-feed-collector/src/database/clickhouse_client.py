"""
ClickHouse Database Client

This module provides a client for interacting with ClickHouse database.
"""

import asyncio
from typing import Dict, List, Any, Optional, Union
from urllib.parse import quote_plus
import numpy as np
from sqlalchemy import create_engine, text
from sqlalchemy.engine import Engine
from clickhouse_driver import Client
import pandas as pd
from loguru import logger
from datetime import datetime

from .schema import ClickHouseSchema

def convert_dataframe_to_match_table(df: pd.DataFrame) -> pd.DataFrame:
    """
    Convert DataFrame column types to match the shoonya.instrument_master table schema.

    Args:
        df: Input DataFrame

    Returns:
        pd.DataFrame: DataFrame with converted column types
    """
    # Define the expected column types based on the table schema
    column_types = {
        "instrument_key": str,
        "exchange_token": str,
        "trading_symbol": str,
        "name": str,
        "exchange": str,
        "segment": str,
        "instrument_type": str,
        "underlying_symbol": str,
        "underlying_key": str,
        "underlying_type": str,
        "strike_price": float,
        "tick_size": float,
        "lot_size": int,
        "minimum_lot": int,
        "freeze_quantity": float,
        "expiry": "datetime64[ms]",  # DateTime64(3) corresponds to milliseconds
        "weekly": int,         # UInt8 corresponds to np.uint8
        "created_at": "datetime64[ms]",
        "updated_at": "datetime64[ms]"
    }

    # Convert each column to the expected type
    for column, dtype in column_types.items():
        if column in df.columns:
            try:
                if dtype == "datetime64[ms]":
                    df[column] = pd.to_datetime(df[column], errors="coerce")
                else:
                    df[column] = df[column].astype(dtype, errors="ignore")
            except Exception as e:
                logger.warning(f"Failed to convert column '{column}' to type {dtype}: {e}")

    return df


class ClickHouseClient:
    """ClickHouse database client with connection management and operations."""

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize ClickHouse client.

        Args:
            config: Database configuration dictionary
        """
        self.config = config
        self.engines: Dict[str, Engine] = {}
        self.clients: Dict[str, Client] = {}
        self.schema = ClickHouseSchema()

        # Initialize connections
        self._initialize_connections()

    def _initialize_connections(self):
        """Initialize database connections for different databases."""
        try:
            # Main database connection (sensibull)
            main_db = self.config['databases']['main']
            main_connection_string = (
                f"clickhouse+http://{self.config['username']}:"
                f"{quote_plus(self.config['password'])}@"
                f"{self.config['host']}:{self.config['port']}/{main_db}"
            )
            self.engines['main'] = create_engine(main_connection_string)
            self.clients['main'] = Client(
                host=self.config['host'],
                port=9000,  # Native port for clickhouse-driver
                user=self.config['username'],
                password=self.config['password'],
                database=main_db
            )

            # AngleOne database connection
            angleone_db = self.config['databases']['angleone']
            angleone_connection_string = (
                f"clickhouse+http://{self.config['username']}:"
                f"{quote_plus(self.config['password'])}@"
                f"{self.config['host']}:{self.config['port']}/{angleone_db}"
            )
            self.engines['angleone'] = create_engine(angleone_connection_string)
            self.clients['angleone'] = Client(
                host=self.config['host'],
                port=9000,
                user=self.config['username'],
                password=self.config['password'],
                database=angleone_db
            )

            logger.info("ClickHouse connections initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize ClickHouse connections: {e}")
            raise

    def test_connection(self, database: str = 'main') -> bool:
        """
        Test database connection.

        Args:
            database: Database name ('main' or 'angleone')

        Returns:
            bool: True if connection is successful
        """
        try:
            with self.engines[database].connect() as conn:
                result = conn.execute(text("SELECT 1"))
                return result.fetchone()[0] == 1
        except Exception as e:
            logger.error(f"Connection test failed for {database}: {e}")
            return False

    def create_tables(self) -> bool:
        """
        Create all required tables and views.

        Returns:
            bool: True if all tables created successfully
        """
        try:
            schemas = self.schema.get_all_schemas()

            with self.engines['main'].connect() as conn:
                for table_name, schema_sql in schemas.items():
                    if table_name == 'equity_latest_prices':
                        # This view references angleone database
                        continue

                    logger.info(f"Creating table: {table_name}")
                    conn.execute(text(schema_sql))
                    conn.commit()

            # Create view in main database that references angleone
            with self.engines['main'].connect() as conn:
                logger.info("Creating equity_latest_prices view")
                conn.execute(text(schemas['equity_latest_prices']))
                conn.commit()

            # Create indexes
            indexes = self.schema.get_indexes()
            with self.engines['main'].connect() as conn:
                for index_name, index_sql in indexes.items():
                    logger.info(f"Creating index: {index_name}")
                    try:
                        conn.execute(text(index_sql))
                        conn.commit()
                    except Exception as e:
                        logger.warning(f"Index creation failed for {index_name}: {e}")

            logger.success("All tables and indexes created successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to create tables: {e}")
            return False
        
    def get_instrument_master(self) -> pd.DataFrame:
        """
        Get instrument master data.

        Returns:
            pd.DataFrame: DataFrame with instrument data
        """
        try:
            query = "SELECT * FROM instrument_master"
            with self.engines['main'].connect() as conn:
                df = pd.read_sql(query, conn)
            return df
        except Exception as e:
            logger.error(f"Failed to get instrument master: {e}")
            return pd.DataFrame()        

    def insert_instrument_master(self, instruments: List[Dict[str, Any]]) -> bool:
        """
        Insert instrument master data.

        Args:
            instruments: List of instrument dictionaries

        Returns:
            bool: True if insertion successful
        """
        try:
            if not instruments:
                logger.warning("No instruments to insert")
                return True

            # Convert to DataFrame for easier handling
            logger.info(f"First instrument: {instruments[0]}")
            df = pd.DataFrame(instruments)

            #this code is added by bhushan from chatgpt
            # Ensure all datetime columns are Python datetimes without timezone
            for col in ["expiry", "created_at", "updated_at"]:
                if col in df.columns:
                    df[col] = pd.to_datetime(df[col]).dt.tz_localize(None)

            # Convert all object columns to plain Python strings
            for col in df.select_dtypes(include=["object"]).columns:
                df[col] = df[col].astype(str)

            # Ensure int64 are cast correctly (ClickHouse doesn’t like numpy.int64 sometimes)
            for col in df.select_dtypes(include=["int64"]).columns:
                df[col] = df[col].astype(int)

            # Ensure float64 are cast to Python float
            for col in df.select_dtypes(include=["float64"]).columns:
                df[col] = df[col].astype(float)

            # Convert to pandas datetime without UTC
            for col in ["expiry", "created_at", "updated_at"]:
                if col in df.columns:
                    df[col] = pd.to_datetime(df[col], utc=True)   # ensure consistent
                    df[col] = df[col].dt.tz_convert("Asia/Kolkata")  # convert timezone
                    df[col] = df[col].dt.tz_localize(None)          # remove tzinfo, ClickHouse expects naive datetime

            for col in ["lot_size", "minimum_lot", "weekly"]:
                if col in df.columns:
                    df[col] = df[col].astype(np.uint32)

            for column in df.columns:
                problematic_rows = df[df[column].apply(lambda x: isinstance(x, np.ndarray))]
                if not problematic_rows.empty:
                    logger.warning(f"Problematic rows in column '{column}': {problematic_rows}")

            # Ensure all columns have supported types
            for column in df.columns:
                logger.info(f"Column: {column}, Type: {df[column].dtype}")
                if df[column].apply(lambda x: isinstance(x, np.ndarray)).any():
                    logger.warning(f"Column '{column}' contains unsupported numpy.ndarray values. Converting to list.")
                    df[column] = df[column].apply(lambda x: x.tolist() if isinstance(x, np.ndarray) else x)
                    logger.info(f"Converted column '{column}' to list. Sample value: {df[column].iloc[0]}")
                df[column] = df[column].apply(lambda x: x.tolist() if isinstance(x, np.ndarray) else x)

            # Convert DataFrame column types to match the table schema
            df = convert_dataframe_to_match_table(df)

            logger.info(f"Inserting {len(df)} instruments into master table")
            # logger.info(f"Sample instrument: {df.head(1)}")

            # # Insert using clickhouse-driver for better performance
            # self.clients['main'].insert_dataframe(
            #     'INSERT INTO instrument_master VALUES',
            #     df
            # )

            # Prepare data for batch insertion

            # Convert DataFrame rows to tuples
            rows = [
                (
                    row['instrument_key'], row['exchange_token'], row['trading_symbol'], row['name'],
                    row['exchange'], row['segment'], row['instrument_type'], row['underlying_symbol'],
                    row['underlying_key'], row['underlying_type'], row['strike_price'], row['tick_size'],
                    row['lot_size'], row['minimum_lot'], row['freeze_quantity'], row['expiry'],
                    row['weekly'], row['created_at'], row['updated_at']
                )
                for _, row in df.iterrows()
            ]

            # Use ClickHouse driver for efficient batch insertion
            query = """
                INSERT INTO instrument_master (
                    instrument_key, exchange_token, trading_symbol, name, exchange, segment,
                    instrument_type, underlying_symbol, underlying_key, underlying_type,
                    strike_price, tick_size, lot_size, minimum_lot, freeze_quantity,
                    expiry, weekly, created_at, updated_at
                ) VALUES
            """

            # Execute batch insert using ClickHouse driver
            self.clients['main'].execute(query, rows)

            logger.success(f"Inserted {len(instruments)} instruments into master table")
            return True

        except Exception as e:
            logger.error(f"Failed to insert instrument master data: {e}")
            return False

    def insert_option_greeks_feed(self, feed_data: List[Dict[str, Any]]) -> bool:
        """
        Insert option Greeks feed data.

        Args:
            feed_data: List of feed data dictionaries

        Returns:
            bool: True if insertion successful
        """
        try:
            if not feed_data:
                return True

            # Prepare batch data
            batch_data = []
            for record in feed_data:
                batch_data.append((
                    record.get('instrument_key', ''),
                    record.get('exchange_token', ''),
                    record.get('trading_symbol', ''),
                    record.get('underlying_symbol', ''),
                    record.get('instrument_type', ''),
                    record.get('lot_size', ''),
                    record.get('weekly', ''),
                    float(record.get('strike_price', 0.0)),
                    #convert expiry to yyyy-mm-dd format, set default date as min date 1970-01-01
                    record.get('expiry').strftime('%Y-%m-%d') if record.get('expiry') else datetime(1970, 1, 1),
                    float(record.get('ltp', 0.0)),
                    record.get('ltt'),
                    int(record.get('ltq', 0)),
                    float(record.get('cp', 0.0)),
                    int(record.get('bid_quantity', 0)),
                    float(record.get('bid_price', 0.0)),
                    int(record.get('ask_quantity', 0)),
                    float(record.get('ask_price', 0.0)),
                    float(record.get('delta', 0.0)),
                    float(record.get('theta', 0.0)),
                    float(record.get('gamma', 0.0)),
                    float(record.get('vega', 0.0)),
                    float(record.get('rho', 0.0)),
                    int(record.get('volume_traded_today', 0)),
                    int(record.get('open_interest', 0)),
                    float(record.get('implied_volatility', 0.0)),
                    record.get('current_ts'),
                    record.get('fetch_time'),
                    record.get('feed_type', 'live_feed')
                ))
            logger.info(f"Batch data: {batch_data[0]}")

            # Use ClickHouse driver for efficient batch insertion
            query = """
                INSERT INTO option_greeks_feed (
                    instrument_key, exchange_token, trading_symbol, underlying_symbol,
                    instrument_type, strike_price, expiry, ltp, ltt, ltq, cp,
                    bid_quantity, bid_price, ask_quantity, ask_price,
                    delta, theta, gamma, vega, rho,
                    volume_traded_today, open_interest, implied_volatility,
                    current_ts, fetch_time, feed_type
                ) VALUES
            """

            # Execute batch insert using ClickHouse driver
            self.clients['main'].execute(query, batch_data)

            logger.debug(f"Inserted {len(feed_data)} feed records")
            return True

        except Exception as e:
            logger.error(f"Failed to insert option Greeks feed data: {e}")
            return False

    def get_equity_prices(self, symbols: Optional[List[str]] = None) -> pd.DataFrame:
        """
        Get latest equity prices from AngleOne data.

        Args:
            symbols: Optional list of symbols to filter

        Returns:
            pd.DataFrame: DataFrame with symbol and last_price columns
        """
        try:
            query = """
            SELECT
                symbol,
                argMax(lastPrice, jobDateTime) AS last_price,
                argMax(jobDateTime, jobDateTime) AS latest_job_datetime
            FROM angleone.view_equity_aone
            WHERE expiryType = 'EQ'
            """

            if symbols:
                symbols_str = "', '".join(symbols)
                query += f" AND symbol IN ('{symbols_str}')"

            query += " GROUP BY symbol ORDER BY latest_job_datetime DESC"

            with self.engines['angleone'].connect() as conn:
                df = pd.read_sql(query, conn)

            logger.info(f"Retrieved equity prices for {len(df)} symbols")
            return df

        except Exception as e:
            logger.error(f"Failed to get equity prices: {e}")
            return pd.DataFrame()

    def get_instruments_by_symbol(self, symbol: str, instrument_types: Optional[List[str]] = None) -> pd.DataFrame:
        """
        Get instruments for a specific underlying symbol.

        Args:
            symbol: Underlying symbol
            instrument_types: Optional list of instrument types to filter

        Returns:
            pd.DataFrame: DataFrame with instrument data
        """
        try:
            query = """
            SELECT *
            FROM instrument_master
            WHERE underlying_symbol = %(symbol)s
            """

            params = {'symbol': symbol}

            if instrument_types:
                placeholders = ', '.join([f"'{t}'" for t in instrument_types])
                query += f" AND instrument_type IN ({placeholders})"

            query += " ORDER BY expiry, strike_price"

            with self.engines['main'].connect() as conn:
                df = pd.read_sql(text(query), conn, params=params)

            return df

        except Exception as e:
            logger.error(f"Failed to get instruments for symbol {symbol}: {e}")
            return pd.DataFrame()

    def log_subscription(self, subscription_data: Dict[str, Any]) -> bool:
        """
        Log subscription activity.

        Args:
            subscription_data: Subscription log data

        Returns:
            bool: True if logging successful
        """
        try:
            # Prepare data for insertion
            batch_data = [(
                subscription_data.get('subscription_id', ''),
                subscription_data.get('action', ''),
                str(subscription_data.get('instrument_keys', [])),  # Convert list to string
                subscription_data.get('connection_id', ''),
                subscription_data.get('status', ''),
                subscription_data.get('error_message', ''),
                int(subscription_data.get('instruments_count', 0))
            )]

            query = """
                INSERT INTO subscription_log
                (subscription_id, action, instrument_keys, connection_id, status, error_message, instruments_count)
                VALUES
            """

            # Execute using ClickHouse driver
            self.clients['main'].execute(query, batch_data)

            return True

        except Exception as e:
            logger.error(f"Failed to log subscription: {e}")
            return False

    def close_connections(self):
        """Close all database connections."""
        try:
            for client in self.clients.values():
                client.disconnect()

            for engine in self.engines.values():
                engine.dispose()

            logger.info("All database connections closed")

        except Exception as e:
            logger.error(f"Error closing database connections: {e}")