"""
Instrument Master Data Handler

This module handles downloading, parsing, and loading NSE instrument data
from Upstox into ClickHouse database.
"""

import gzip
import json
import requests
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional
import pandas as pd
from loguru import logger

from ..database.clickhouse_client import ClickHouseClient


class InstrumentMasterHandler:
    """Handler for NSE instrument master data operations."""

    def __init__(self, config: Dict[str, Any], db_client: ClickHouseClient):
        """
        Initialize instrument master handler.

        Args:
            config: Configuration dictionary
            db_client: ClickHouse database client
        """
        self.config = config
        self.db_client = db_client
        self.instrument_url = config.get('url', 'https://assets.upstox.com/market-quote/instruments/exchange/NSE.json.gz')
        self.download_on_startup = config.get('download_on_startup', True)
        self.refresh_interval_hours = config.get('refresh_interval_hours', 24)

        # Cache for instruments
        self._instruments_cache: Optional[List[Dict[str, Any]]] = None
        self._last_download_time: Optional[datetime] = None

    def should_refresh_instruments(self) -> bool:
        """
        Check if instruments should be refreshed based on configuration.

        Returns:
            bool: True if refresh is needed
        """
        if not self.download_on_startup:
            return False

        if self._last_download_time is None:
            return True

        hours_since_last_download = (
            datetime.now(timezone.utc) - self._last_download_time
        ).total_seconds() / 3600

        return hours_since_last_download >= self.refresh_interval_hours

    def download_instruments(self) -> bool:
        """
        Download instrument master data from Upstox.

        Returns:
            bool: True if download successful
        """
        try:
            logger.info(f"Downloading instrument master from: {self.instrument_url}")

            # Download the gzipped JSON file
            response = requests.get(self.instrument_url, timeout=30)
            response.raise_for_status()

            # Decompress and parse JSON
            decompressed_data = gzip.decompress(response.content)
            instruments_data = json.loads(decompressed_data.decode('utf-8'))

            logger.info(f"Downloaded {len(instruments_data)} instruments")

            # Filter and process instruments
            processed_instruments = self._process_instruments(instruments_data)

            # Cache the instruments
            self._instruments_cache = processed_instruments
            self._last_download_time = datetime.now(timezone.utc)

            logger.success(f"Processed {len(processed_instruments)} instruments")
            return True

        except requests.RequestException as e:
            logger.error(f"Failed to download instruments: {e}")
            return False
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse instrument JSON: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error downloading instruments: {e}")
            return False

    def _process_instruments(self, raw_instruments: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process and filter raw instrument data.

        Args:
            raw_instruments: Raw instrument data from Upstox

        Returns:
            List: Processed instrument data
        """
        processed = []

        for instrument in raw_instruments:
            try:
                # Filter for NSE_FO (Futures & Options) and NSE_EQ (Equity)
                if instrument.get('segment') not in ['NSE_FO', 'NSE_EQ']:
                    continue

                # Convert expiry timestamp to datetime
                expiry_ts = instrument.get('expiry')
                if expiry_ts:
                    expiry_dt = datetime.fromtimestamp(expiry_ts / 1000, tz=timezone.utc)
                else:
                    # For equity instruments, set a far future date
                    expiry_dt = datetime(2099, 12, 31, tzinfo=timezone.utc)

                processed_instrument = {
                    'instrument_key': instrument.get('instrument_key', ''),
                    'exchange_token': instrument.get('exchange_token', ''),
                    'trading_symbol': instrument.get('trading_symbol', ''),
                    'name': instrument.get('name', ''),
                    'exchange': instrument.get('exchange', ''),
                    'segment': instrument.get('segment', ''),
                    'instrument_type': instrument.get('instrument_type', ''),
                    'underlying_symbol': instrument.get('underlying_symbol', ''),
                    'underlying_key': instrument.get('underlying_key', ''),
                    'underlying_type': instrument.get('underlying_type', ''),
                    'strike_price': float(instrument.get('strike_price', 0.0)),
                    'tick_size': float(instrument.get('tick_size', 0.0)),
                    'lot_size': int(instrument.get('lot_size', 0)),
                    'minimum_lot': int(instrument.get('minimum_lot', 0)),
                    'freeze_quantity': float(instrument.get('freeze_quantity', 0.0)),
                    'expiry': expiry_dt,
                    'weekly': 1 if instrument.get('weekly', False) else 0,
                    'created_at': datetime.now(timezone.utc),
                    'updated_at': datetime.now(timezone.utc)
                }

                processed.append(processed_instrument)

            except (ValueError, TypeError) as e:
                logger.warning(f"Skipping invalid instrument: {e}")
                continue

        return processed
    
    #Load instruments from DB and cache them as List[Dict[str, Any]]
    def load_instruments_from_database(self) -> bool:
        """
        Load instruments from ClickHouse database and cache them.

        Returns:
            bool: True if loading successful
        """
        try:
            # Fetch instruments from database
            instruments_df = self.db_client.get_instrument_master()

            # Convert to list of dictionaries
            instruments = instruments_df.to_dict('records')

            # Cache the instruments
            self._instruments_cache = instruments
            logger.success(f"Loaded {len(instruments)} instruments from database")
            #set the self._last_download_time = max time from created_at
            self._last_download_time = instruments_df['created_at'].max()
            return True

        except Exception as e:
            logger.error(f"Failed to load instruments from database: {e}")
            return False    

    def load_instruments_to_database(self) -> bool:
        """
        Load processed instruments into ClickHouse database.

        Returns:
            bool: True if loading successful
        """
        try:
            if not self._instruments_cache:
                logger.warning("No instruments in cache to load")
                return False

            # Insert instruments into database
            success = self.db_client.insert_instrument_master(self._instruments_cache)

            if success:
                logger.success(f"Loaded {len(self._instruments_cache)} instruments into database")
            else:
                logger.error("Failed to load instruments into database")

            return success

        except Exception as e:
            logger.error(f"Error loading instruments to database: {e}")
            return False

    def get_fno_symbols(self) -> List[str]:
        """
        Get list of unique FnO underlying symbols.

        Returns:
            List: List of FnO symbols
        """
        try:
            if not self._instruments_cache:
                logger.warning("No instruments in cache")
                return []

            fno_symbols = set()
            for instrument in self._instruments_cache:
                if (instrument.get('segment') == 'NSE_FO' and
                    instrument.get('instrument_type') in ['FUT', 'CE', 'PE']):
                    fno_symbols.add(instrument.get('underlying_symbol'))

            symbols_list = sorted(list(fno_symbols))
            logger.info(f"Found {len(symbols_list)} unique FnO symbols")
            return symbols_list

        except Exception as e:
            logger.error(f"Error getting FnO symbols: {e}")
            return []

    def get_instruments_by_symbol(self, symbol: str, instrument_types: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        Get instruments for a specific underlying symbol.

        Args:
            symbol: Underlying symbol
            instrument_types: Optional list of instrument types to filter

        Returns:
            List: List of matching instruments
        """
        try:
            if not self._instruments_cache:
                logger.warning("No instruments in cache")
                return []

            matching_instruments = []
            for instrument in self._instruments_cache:
                if instrument.get('underlying_symbol') == symbol:
                    if instrument_types is None or instrument.get('instrument_type') in instrument_types:
                        matching_instruments.append(instrument)

            # Sort by expiry and strike price
            matching_instruments.sort(key=lambda x: (x.get('expiry', datetime.min), x.get('strike_price', 0)))

            return matching_instruments

        except Exception as e:
            logger.error(f"Error getting instruments for symbol {symbol}: {e}")
            return []

    def refresh_instruments(self) -> bool:
        """
        Refresh instrument master data if needed.

        Returns:
            bool: True if refresh successful or not needed
        """
        try:
            if not self.should_refresh_instruments():
                logger.info("Instrument refresh not needed")
                #load from database
                if not self.load_instruments_from_database():
                    return False
                return True

            logger.info("Refreshing instrument master data")

            # Download instruments
            if not self.download_instruments():
                return False

            # Load to database
            if not self.load_instruments_to_database():
                return False

            logger.success("Instrument master data refreshed successfully")
            return True

        except Exception as e:
            logger.error(f"Error refreshing instruments: {e}")
            return False

    def get_cached_instruments(self) -> Optional[List[Dict[str, Any]]]:
        """
        Get cached instruments.

        Returns:
            List: Cached instruments or None if not available
        """
        return self._instruments_cache

    def clear_cache(self):
        """Clear the instruments cache."""
        self._instruments_cache = None
        self._last_download_time = None
        logger.info("Instrument cache cleared")