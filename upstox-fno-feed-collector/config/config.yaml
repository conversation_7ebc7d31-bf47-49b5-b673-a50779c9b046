# Upstox FnO Feed Collector Configuration

# Upstox API Configuration
upstox:
  api_key: "b2fa38fd-ddbb-4ed0-8dbd-303bf292ebae"  # Your Upstox API Key
  secret_key: "lleo1o460s"  # Your Upstox Secret Key
  redirect_uri: "http://localhost:8000"
  totp_key: "GJ7KWHB4ZVAMPWGDWQ52PX5II6VS3QFE"  # Your TOTP Key for 2FA
  mobile_no: "9970440910"  # Your registered mobile number
  pin: "612713"  # Your 6-digit PIN

# ClickHouse Database Configuration
clickhouse:
  host: "localhost"
  port: 8123
  username: "scorepandit"
  password: "Score@1615"
  databases:
    main: "shoonya"  # Main database for storing feed data
    angleone: "angleone"  # AngleOne database for equity prices

# Strike Configuration
strikes:
  range: 2  # ATM ± 2 strikes (configurable)
  refresh_interval_minutes: 30  # Subscription refresh interval

# WebSocket Configuration
websocket:
  max_connections: 1  # Upstox limit: 5 concurrent connections
  max_instruments_per_connection: 3000  # Upstox limit per connection
  reconnect_attempts: 5
  reconnect_delay_seconds: 5

# Instrument Master Configuration
instrument_master:
  download_on_startup: false  # Whether to download instrument master on startup
  url: "https://assets.upstox.com/market-quote/instruments/exchange/NSE.json.gz"
  refresh_interval_hours: 24  # How often to refresh instrument master

# Protobuf Configuration
protobuf:
  proto_url: "https://assets.upstox.com/feed/market-data-feed/v3/MarketDataFeed.proto"

# Logging Configuration
logging:
  level: "DEBUG"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  file_rotation: "1 day"  # Daily log rotation
  file_retention: "30 days"  # Keep logs for 30 days
  max_file_size: "100 MB"
  format: "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"

# Application Configuration
app:
  name: "upstox-fno-feed-collector"
  version: "1.0.0"
  timezone: "Asia/Kolkata"
  graceful_shutdown_timeout: 30  # seconds

# Error Handling Configuration
error_handling:
  max_retries: 3
  retry_delay_seconds: 5
  circuit_breaker_threshold: 10  # Number of failures before circuit breaker opens
  circuit_breaker_timeout_seconds: 60