2025-08-19 00:01:01.340 | ERROR    | src.core.subscription_manager:refresh_subscriptions:129 - No FnO symbols found
2025-08-19 00:01:01.340 | ERROR    | src.core.subscription_manager:start:74 - Failed to perform initial subscription
2025-08-19 00:01:01.340 | ERROR    | src.main:start:267 - Failed to start subscription manager
2025-08-19 00:01:01.340 | ERROR    | src.main:run:328 - Failed to start application
2025-08-19 00:11:44.987 | ERROR    | src.data.strike_calculator:get_subscription_instruments:275 - Error getting subscription instruments: All arrays must be of the same length
2025-08-19 00:11:44.987 | ERROR    | src.core.subscription_manager:refresh_subscriptions:138 - No instrument keys generated for subscription
2025-08-19 00:11:44.987 | ERROR    | src.core.subscription_manager:start:74 - Failed to perform initial subscription
2025-08-19 00:11:44.987 | ERROR    | src.main:start:267 - Failed to start subscription manager
2025-08-19 00:11:44.987 | ERROR    | src.main:run:328 - Failed to start application
2025-08-19 00:18:49.623 | ERROR    | src.data.instrument_master:load_instruments_from_database:178 - Failed to load instruments from database: 'ClickHouseClient' object has no attribute 'get_instrument_master'
2025-08-19 00:18:49.623 | ERROR    | src.core.subscription_manager:refresh_subscriptions:123 - Failed to refresh instrument master
2025-08-19 00:18:49.623 | ERROR    | src.core.subscription_manager:start:74 - Failed to perform initial subscription
2025-08-19 00:18:49.623 | ERROR    | src.main:start:273 - Failed to start subscription manager
2025-08-19 00:18:49.623 | ERROR    | src.main:run:334 - Failed to start application
2025-08-19 00:20:03.078 | ERROR    | src.data.strike_calculator:get_subscription_instruments:275 - Error getting subscription instruments: All arrays must be of the same length
2025-08-19 00:20:03.078 | ERROR    | src.core.subscription_manager:refresh_subscriptions:138 - No instrument keys generated for subscription
2025-08-19 00:20:03.079 | ERROR    | src.core.subscription_manager:start:74 - Failed to perform initial subscription
2025-08-19 00:20:03.079 | ERROR    | src.main:start:273 - Failed to start subscription manager
2025-08-19 00:20:03.079 | ERROR    | src.main:run:334 - Failed to start application
2025-08-19 00:24:48.293 | ERROR    | src.data.strike_calculator:get_subscription_instruments:275 - Error getting subscription instruments: All arrays must be of the same length
2025-08-19 00:24:48.293 | ERROR    | src.core.subscription_manager:refresh_subscriptions:138 - No instrument keys generated for subscription
2025-08-19 00:24:48.293 | ERROR    | src.core.subscription_manager:start:74 - Failed to perform initial subscription
2025-08-19 00:24:48.293 | ERROR    | src.main:start:273 - Failed to start subscription manager
2025-08-19 00:24:48.293 | ERROR    | src.main:run:334 - Failed to start application
2025-08-19 00:44:28.067 | ERROR    | src.websocket.connection_manager:connect:87 - Connection ws_conn_2: Failed to connect: server rejected WebSocket connection: HTTP 403
2025-08-19 00:44:28.068 | ERROR    | src.websocket.connection_manager:initialize_connections:294 - Failed to initialize connection ws_conn_2
2025-08-19 00:44:28.214 | ERROR    | src.websocket.connection_manager:connect:87 - Connection ws_conn_3: Failed to connect: server rejected WebSocket connection: HTTP 403
2025-08-19 00:44:28.214 | ERROR    | src.websocket.connection_manager:initialize_connections:294 - Failed to initialize connection ws_conn_3
2025-08-19 18:01:10.304 | ERROR    | src.websocket.connection_manager:connect:120 - Connection ws_conn_3: Failed to connect: server rejected WebSocket connection: HTTP 403
2025-08-19 18:01:10.304 | ERROR    | src.websocket.connection_manager:initialize_connections:327 - Failed to initialize connection ws_conn_3
2025-08-19 18:27:25.633 | ERROR    | src.database.clickhouse_client:insert_option_greeks_feed:376 - Failed to insert option Greeks feed data: 'NoneType' object has no attribute 'tzinfo'
2025-08-19 18:27:25.634 | ERROR    | src.websocket.feed_processor:_flush_buffer:269 - Failed to insert 2 records
2025-08-19 18:49:05.579 | ERROR    | src.database.clickhouse_client:create_tables:184 - Failed to create tables: Orig exception: Code: 44. DB::Exception: Partition key contains nullable columns, but merge tree setting `allow_nullable_key` is disabled. (ILLEGAL_COLUMN) (version 24.6.1.3290 (official build))

2025-08-19 18:49:05.579 | ERROR    | src.main:_initialize_components:185 - Failed to create database tables
2025-08-19 18:49:05.579 | ERROR    | src.main:run:334 - Failed to start application
2025-08-19 18:51:06.507 | ERROR    | src.database.clickhouse_client:insert_option_greeks_feed:378 - Failed to insert option Greeks feed data: 'str' object has no attribute 'year'
2025-08-19 18:51:06.508 | ERROR    | src.websocket.feed_processor:_flush_buffer:269 - Failed to insert 2 records
2025-08-19 18:52:50.138 | ERROR    | src.database.clickhouse_client:insert_option_greeks_feed:379 - Failed to insert option Greeks feed data: 'str' object has no attribute 'year'
2025-08-19 18:52:50.140 | ERROR    | src.websocket.feed_processor:_flush_buffer:269 - Failed to insert 2 records
2025-08-19 18:58:50.497 | ERROR    | src.database.clickhouse_client:insert_option_greeks_feed:379 - Failed to insert option Greeks feed data: Code: 53. Type mismatch in VALUES section. Repeat query with types_check=True for detailed info. Column open_interest: required argument is not an integer
2025-08-19 18:58:50.497 | ERROR    | src.websocket.feed_processor:_flush_buffer:269 - Failed to insert 2 records
2025-08-19 19:17:30.683 | ERROR    | src.database.clickhouse_client:insert_option_greeks_feed:379 - Failed to insert option Greeks feed data: Code: 53. Type mismatch in VALUES section. Repeat query with types_check=True for detailed info. Column open_interest: required argument is not an integer
2025-08-19 19:17:30.684 | ERROR    | src.websocket.feed_processor:_flush_buffer:269 - Failed to insert 2 records
